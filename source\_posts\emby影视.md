---
title: emby影视
date: 2025-06-08 16:13:17
tags: [Emby, 影视, 流媒体]
categories: [影视娱乐]
cover: https://cdn4.winhlb.com/2025/06/08/684545d0c695c.png
description: 69云机场提供的Emby影视服务，包括DPX硬盘服、50万+资源服和Porn教学服
---

<style>
.terminal-block {
    background: #1e1e1e;
    border: 1px solid #333;
    border-radius: 6px;
    margin: 10px 0;
    position: relative;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.terminal-header {
    background: #2d2d2d;
    padding: 8px 12px;
    border-bottom: 1px solid #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 6px 6px 0 0;
}

.terminal-title {
    color: #888;
    font-size: 12px;
    font-weight: normal;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f56; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #27ca3f; }

.terminal-content {
    padding: 12px;
    color: #f8f8f2;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
}

.terminal-prompt {
    color: #50fa7b;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #44475a;
    border: 1px solid #6272a4;
    color: #f8f8f2;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.copy-btn:hover {
    background: #6272a4;
    border-color: #8be9fd;
}

.copy-btn.success {
    background: #50fa7b;
    color: #282a36;
    border-color: #50fa7b;
}

.copy-btn.error {
    background: #ff5555;
    border-color: #ff5555;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.innerHTML;
        button.innerHTML = '✓ 已复制';
        button.className = 'copy-btn success';
        setTimeout(function() {
            button.innerHTML = originalText;
            button.className = 'copy-btn';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
        const originalText = button.innerHTML;
        button.innerHTML = '✗ 失败';
        button.className = 'copy-btn error';
        setTimeout(function() {
            button.innerHTML = originalText;
            button.className = 'copy-btn';
        }, 2000);
    });
}
</script>
## 服务简介

> 💥 **69云机场新增Emby权益公告** 💥
>
> ⚠️ **重要提示：** 为了更好的观影体验，建议在全局 + 69云机场的Emby节点使用emby服务！

## 服务列表

目前，69云为用户提供三大Emby权益：

### 1️⃣ DPX 硬盘服
**权限：** 全体会员尊享

**访问地址1：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">访问地址1</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://emby1.69yun69.com:18690
        <button class="copy-btn" onclick="copyTerminalContent('https://emby1.69yun69.com:18690', this)">复制</button>
    </div>
</div>

**访问地址2：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">访问地址2</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://emby1.69yun69.com:443
        <button class="copy-btn" onclick="copyTerminalContent('https://emby1.69yun69.com:443', this)">复制</button>
    </div>
</div>

### 2️⃣ 50万+资源服
**权限：** 会员级别≥10专享

**访问地址：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">访问地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://emby3.69yun69.com:443
        <button class="copy-btn" onclick="copyTerminalContent('https://emby3.69yun69.com:443', this)">复制</button>
    </div>
</div>

### 3️⃣ Porn教学服
**权限：** 半年卡/年卡用户独享

**访问地址：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">访问地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://emby2.69yun69.com:443
        <button class="copy-btn" onclick="copyTerminalContent('https://emby2.69yun69.com:443', this)">复制</button>
    </div>
</div>

## 账号信息

**👤 Emby 账号：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">Emby 账号</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>您注册69云机场的邮箱（示例：<EMAIL>）
        <button class="copy-btn" onclick="copyTerminalContent('您注册69云机场的邮箱（示例*******************）', this)">复制</button>
    </div>
</div>

**🔑 密码：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">密码</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>空（无需密码）
        <button class="copy-btn" onclick="copyTerminalContent('', this)">复制</button>
    </div>
</div>

## 使用方法

**购买套餐：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">购买套餐</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://china.69yun69.com
        <button class="copy-btn" onclick="copyTerminalContent('https://china.69yun69.com', this)">复制</button>
    </div>
</div>

1. 访问上述地址购买套餐
2. **下载应用：** 下载对应平台的Emby客户端
3. **配置连接：** 在应用中填写服务器地址、端口和账号信息  

## 🔔 重要提示

### ❌ 使用限制
- **禁止网页观看** - 请务必使用第三方App软件进行观看，网页端不支持播放
- **无技术支持** - 69云机场不提供Emby服务的技术支持，如有问题请自行查阅资料

### ✅ 使用建议
- 连接协议请选择 "Emby" 或 "其他Emby"
- 建议使用推荐的客户端应用以获得最佳体验

---

## 📱 推荐客户端

> 💡 **说明：** 以下客户端列表仅供参考，不提供技术支持。连接协议请选择 "Emby" 或 "其他Emby"  

### 🍎 iOS & iPadOS

| 应用名称 | 下载链接 |
|----------|----------|
| Infuse | [App Store](https://apps.apple.com/cn/app/id1136220934) |
| yybx | [App Store](https://apps.apple.com/cn/app/yybx/id1519723194) |
| iemc | [App Store](https://apps.apple.com/cn/app/iemc/id1467462861) |
| Fileball | [App Store](https://apps.apple.com/cn/app/fileball/id1558391784) |
| HamHub | [App Store](https://apps.apple.com/cn/app/id6458691598) |
| SenPlayer | [App Store](https://apps.apple.com/cn/app/id6443975850) |
| Conflux | [App Store](https://apps.apple.com/cn/app/conflux-video-player/id6450330892) |
| iPlay | [App Store](https://apps.apple.com/cn/app/iplay/id6480576133) |
| Forward | [App Store](https://apps.apple.com/us/app/forward-%E6%96%B0%E8%A7%86%E7%95%8C/id6503940939) |

### 📺 Apple TV

| 应用名称 | 下载链接 |
|----------|----------|
| Infuse | [App Store](https://apps.apple.com/cn/app/id1136220934) |
| yybx | [App Store](https://apps.apple.com/cn/app/yybx/id1519723194) |
| Fileball | [App Store](https://apps.apple.com/cn/app/fileball/id1558391784) |
| HamHub | [App Store](https://apps.apple.com/cn/app/id6458691598) |
| SenPlayer | [App Store](https://apps.apple.com/cn/app/id6443975850) |
| Conflux | [App Store](https://apps.apple.com/cn/app/conflux-video-player/id6450330892) |
| iPlay | [App Store](https://apps.apple.com/cn/app/iplay/id6480576133) |

> ⚠️ **使用限制：** 禁止使用Vidhub登录，禁止开启Infuse的"媒体库模式"功能，禁止通过网页端登录，禁止使用Reflix连接，禁止使用Emby for Kodi登录。登录成功后，请不要新建播放列表。  

### 🤖 Android

| 应用名称 | 下载链接 |
|----------|----------|
| Afusekt | [GitHub](https://github.com/AttemptD/AfuseKt-release/releases) |
| Yamby | [Telegram](https://t.me/yamby_release) |
| Emby 小秘版 | [Telegram](https://t.me/NaoRubbish/119) |
| iPlay | [GitHub](https://github.com/ourfor/iPlay/releases) |
| Jellyfin | [Google Play](https://play.google.com/store/apps/details?id=org.jellyfin.mobile&hl=en_US) |
| Findroid | [Google Play](https://play.google.com/store/apps/details?id=dev.jdtech.jellyfin&hl=en_US) |
| Emby kirlif版 | [Telegram](https://t.me/SaltSoupGarage/603) |
| Femor | [Telegram](https://t.me/hkaemby/38362) |

### 📺 Android TV

| 应用名称 | 下载链接 |
|----------|----------|
| Afusekt TV版 | [GitHub](https://github.com/AttemptD/AfuseKtV-release/releases) |
| Jellyfin Android TV | [GitHub](https://github.com/jellyfin/jellyfin-androidtv/releases) |
| Emby 小秘版 | [Telegram](https://t.me/NaoRubbish/119) |
| Emby kirlif版 | [Telegram](https://t.me/SaltSoupGarage/615) |

> 💡 **提示：** Emby的各种改版在不同设备上兼容性表现不同，建议多次尝试后选择体验最好的版本。

### 💻 Windows

| 应用名称 | 下载链接 |
|----------|----------|
| Emby 小秘版 | [Telegram](https://t.me/NaoRubbish/119) |
| Tsukimi | [GitHub](https://github.com/tsukinaha/tsukimi/releases) |
| Femor | [Telegram](https://t.me/hkaemby/38362) |
| Jellyfin Media Player | [GitHub](https://github.com/jellyfin/jellyfin-media-player/releases) |

## 免责声明

本文档仅用于学习和教育目的，旨在帮助安全研究人员和开发者了解。本博文中所包含的信息和工具仅用于合法的安全测试和研究，不得用于任何非法活动。

使用本文所提供的信息进行任何未经授权的行为均为非法行为，违反法律将导致严重的法律后果。读者在使用这些信息时，必须确保拥有合法的授权，并严格遵守所在国家和地区的法律法规。

作者不对任何因使用本文内容而导致的直接或间接损害承担责任。所有风险和责任由用户自行承担。

此外，读者必须在24小时内删除产生的内容，以确保信息不会被滥用。

