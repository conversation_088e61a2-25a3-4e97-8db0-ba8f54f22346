/* Custom CSS for Solitude theme */
#banners {
  background-image: url('/img/1.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  position: relative !important;
}

/* 左侧横幅时钟组件 */
#banner-clock {
  position: absolute;
  bottom: 20px;
  left: 20px;
  width: 200px;
  height: 150px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 15px;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.3s ease;
}

#banner-clock:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.2);
}

.banner-clock-time {
  font-size: 1.6rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  line-height: 1.2;
}

.banner-clock-date {
  font-size: 0.75rem;
  text-align: center;
  opacity: 0.9;
  margin-bottom: 6px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.banner-clock-weather {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 0.65rem;
  opacity: 0.9;
}

.banner-clock-weather-item {
  display: flex;
  align-items: center;
  gap: 3px;
  white-space: nowrap;
}

.banner-clock-weather-item i {
  font-size: 0.6rem;
}

.banner-clock-location {
  font-size: 0.7rem;
  text-align: center;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 8px;
  position: relative;
}

.banner-clock-location:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.banner-clock-location:active {
  transform: translateY(0);
}

.banner-clock-location i {
  font-size: 0.7rem;
}

/* 位置点击提示 */
.banner-clock-location::after {
  content: '点击更改';
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  white-space: nowrap;
  z-index: 1000;
}

.banner-clock-location:hover::after {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  #banner-clock {
    width: 160px;
    height: 130px;
    bottom: 15px;
    left: 15px;
    padding: 12px;
  }

  .banner-clock-time {
    font-size: 1.2rem;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
  }

  .banner-clock-date {
    font-size: 0.65rem;
    margin-bottom: 4px;
  }

  .banner-clock-weather {
    font-size: 0.55rem;
    margin-bottom: 4px;
  }

  .banner-clock-weather-item i {
    font-size: 0.5rem;
  }

  .banner-clock-location {
    font-size: 0.6rem;
    gap: 3px;
  }
}

/* 深色模式适配 */
[data-theme="dark"] #banner-clock {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] #banner-clock:hover {
  background: rgba(0, 0, 0, 0.4);
}

/* Mobile optimization for info card and clock widget */
@media (max-width: 768px) {
  #aside-content {
    display: block !important;
    width: 100% !important;
    margin-top: 1rem;
    order: 2;
  }
  .layout > div:first-child:not(.recent-posts) {
    width: 100% !important;
  }
  .card-widget.card-info,
  .card-widget.card-clock {
    width: 90% !important;
    margin: 1rem auto !important;
    min-height: auto !important;
  }
  .clock-time {
    font-size: 1.5rem !important;
  }
  .clock-header,
  .clock-location {
    font-size: 0.8rem !important;
  }

  /* 移动端文章宽度优化 */
  #post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 15px !important;
    box-sizing: border-box !important;
  }
  
  #article-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .layout {
    padding: 0 10px !important;
  }
  
  #post .post-copyright,
  #post .post-reward,
  #post .post-tags,
  #post .post-nav {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* 移动端文章内容区域优化 */
  .post-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }
  
  /* 移动端文章整体布局优化 */
  .layout > div:first-child {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端文章主体容器 */
  .post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 10px !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章内部容器 */
  #post > .post-content,
  .post > .post-content {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端markdown内容优化 */
  .markdown-body {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章卡片样式 */
  .card-post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章标题优化 */
  .post-title {
    width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  
  /* 移动端图片优化 */
  .post-content img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 10px auto !important;
  }
  
  /* 移动端列表优化 */
  .post-content ul,
  .post-content ol {
    padding-left: 20px !important;
    margin: 10px 0 !important;
  }
  
  /* 移动端引用块优化 */
  .post-content blockquote {
    margin: 10px 0 !important;
    padding: 10px 15px !important;
    border-left: 4px solid #ddd !important;
  }
  
  /* 移动端terminal-block优化 */
  .terminal-block {
    margin: 15px -10px !important;
    border-radius: 0 !important;
  }
  
  /* 强制所有文章容器100%宽度 */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 特殊处理文章页面 */
  body:has(.post) .layout {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端代码块优化 */
  .highlight {
    margin: 0 -15px !important;
    border-radius: 0 !important;
  }
  
  /* 移动端表格优化 */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}