/* Custom CSS for Solitude theme */
#banners {
  background-image: url('/img/1.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  position: relative !important;
}





/* Mobile optimization for info card and clock widget */
@media (max-width: 768px) {
  #aside-content {
    display: block !important;
    width: 100% !important;
    margin-top: 1rem;
    order: 2;
  }
  .layout > div:first-child:not(.recent-posts) {
    width: 100% !important;
  }
  .card-widget.card-info,
  .card-widget.card-clock {
    width: 90% !important;
    margin: 1rem auto !important;
    min-height: auto !important;
  }
  .clock-time {
    font-size: 1.5rem !important;
  }
  .clock-header,
  .clock-location {
    font-size: 0.8rem !important;
  }

  /* 移动端文章宽度优化 */
  #post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 15px !important;
    box-sizing: border-box !important;
  }
  
  #article-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .layout {
    padding: 0 10px !important;
  }
  
  #post .post-copyright,
  #post .post-reward,
  #post .post-tags,
  #post .post-nav {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* 移动端文章内容区域优化 */
  .post-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
  }
  
  /* 移动端文章整体布局优化 */
  .layout > div:first-child {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端文章主体容器 */
  .post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 10px !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章内部容器 */
  #post > .post-content,
  .post > .post-content {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端markdown内容优化 */
  .markdown-body {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章卡片样式 */
  .card-post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }
  
  /* 移动端文章标题优化 */
  .post-title {
    width: 100% !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }
  
  /* 移动端图片优化 */
  .post-content img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 10px auto !important;
  }
  
  /* 移动端列表优化 */
  .post-content ul,
  .post-content ol {
    padding-left: 20px !important;
    margin: 10px 0 !important;
  }
  
  /* 移动端引用块优化 */
  .post-content blockquote {
    margin: 10px 0 !important;
    padding: 10px 15px !important;
    border-left: 4px solid #ddd !important;
  }
  
  /* 移动端terminal-block优化 */
  .terminal-block {
    margin: 15px -10px !important;
    border-radius: 0 !important;
  }
  
  /* 强制所有文章容器100%宽度 */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 特殊处理文章页面 */
  body:has(.post) .layout {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 移动端代码块优化 */
  .highlight {
    margin: 0 -15px !important;
    border-radius: 0 !important;
  }
  
  /* 移动端表格优化 */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }
}