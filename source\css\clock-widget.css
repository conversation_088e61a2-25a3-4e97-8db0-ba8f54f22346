/* 时钟组件样式 */
.card-clock {
  margin-bottom: 1rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 0 !important;
  background: transparent;
}

.card-clock:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.clock-widget {
  padding: 0 !important;
  margin: 0 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  flex: 1;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
}

.clock-widget::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.clock-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.8rem;
  font-size: 0.85rem;
  opacity: 0.9;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem 1.5rem 0.5rem 1.5rem;
}

.clock-date {
  font-weight: 500;
}

.clock-weather {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  flex-wrap: wrap;
}

.clock-weather i {
  font-size: 0.8rem;
}

.clock-time {
  font-size: 2.2rem;
  font-weight: 700;
  text-align: center;
  margin: 0;
  padding: 1rem 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.clock-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.9;
  padding: 0.5rem 1.5rem 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  position: relative;
}

.clock-location:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

[data-theme="dark"] .clock-location:hover {
  background: rgba(255, 255, 255, 0.05);
}

.clock-location:active {
  transform: translateY(0);
}

.clock-location i {
  font-size: 0.8rem;
}

/* 侧边栏位置点击提示 */
.clock-location::after {
  content: '点击更改位置';
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 11px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  white-space: nowrap;
  z-index: 1000;
}

[data-theme="dark"] .clock-location::after {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.clock-location:hover::after {
  opacity: 1;
}

/* 深色模式适配 */
[data-theme="dark"] .card-clock {
  background: var(--efu-card-bg);
  border: 1px solid var(--efu-border-color);
}

[data-theme="dark"] .clock-widget {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .clock-widget {
    padding: 0;
    min-height: 100%;
  }
  
  .clock-time {
    font-size: 1.8rem;
    padding: 0.8rem 1rem;
  }
  
  .clock-header {
    font-size: 0.8rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
    padding: 0.8rem 1rem 0.3rem 1rem;
  }
  
  .clock-location {
    font-size: 0.85rem;
    padding: 0.3rem 1rem 0.8rem 1rem;
  }
  
  .clock-weather {
    gap: 0.3rem;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.clock-widget {
  animation: fadeInUp 0.6s ease-out;
}

.clock-time {
  transition: all 0.3s ease;
}

.clock-time:hover {
  transform: scale(1.05);
}

/* 卡片标题样式 */
.card-clock .item-headline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1rem 0.5rem;
  font-weight: 600;
  color: var(--efu-fontcolor);
  border-bottom: 1px solid var(--efu-border-color);
  margin-bottom: 0;
}

.card-clock .item-headline i {
  color: var(--efu-main);
  font-size: 1.1rem;
}

.card-clock .card-content {
  padding: 0 !important;
  margin: 0 !important;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  border-radius: 12px;
}

/* 自动主题切换控制区域 */
.clock-controls {
  padding: 0.8rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}

.auto-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
}

.auto-theme-toggle i {
  font-size: 0.9rem;
  margin-right: 0.3rem;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  transition: 0.3s;
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: rgba(255, 255, 255, 0.6);
}

input:checked + .slider:before {
  transform: translateX(20px);
  background-color: #4CAF50;
}

.slider:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

input:checked + .slider:hover {
  background-color: rgba(255, 255, 255, 0.7);
}

/* 深色模式下的开关样式 */
[data-theme="dark"] .clock-controls {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

/* 天气图标动画 */
.clock-weather i {
  transition: transform 0.3s ease;
}

.clock-weather i:hover {
  transform: scale(1.2);
}

/* 位置图标动画 */
.clock-location i {
  transition: color 0.3s ease;
}

.clock-location:hover i {
  color: #ff6b6b;
}