---
title: {{ title }}
date: {{ date }}
cover: imgs/covers/default-cover.svg
# 可选封面模板:
# imgs/covers/tech-cover-1.svg - 技术分享(紫色)
# imgs/covers/tech-cover-2.svg - 开发教程(蓝色)
# imgs/covers/ai-cover.svg - AI应用(粉橙色)
# imgs/covers/tools-cover.svg - 实用工具(青粉色)
# imgs/covers/default-cover.svg - 默认封面(橙色)
tags:
categories:
description:
keywords: []
---

<style>
.terminal-block {
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    font-family: 'Courier New', monospace;
}

.terminal-header {
    background: linear-gradient(90deg, #3a3a3a 0%, #4a4a4a 100%);
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #555;
}

.terminal-title {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
}

.terminal-buttons {
    display: flex;
    gap: 6px;
}

.terminal-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
}

.terminal-btn.close { background: #ff5f57; }
.terminal-btn.minimize { background: #ffbd2e; }
.terminal-btn.maximize { background: #28ca42; }

.terminal-content {
    padding: 15px;
    color: #00ff00;
    font-size: 14px;
    position: relative;
    background: #1e1e1e;
}

.terminal-prompt {
    color: #00ff00;
    margin-right: 8px;
}

.copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-btn:hover {
    background: #005a9e;
}
</style>

<script>
function copyTerminalContent(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalText = button.textContent;
        button.textContent = '已复制';
        button.style.background = '#28a745';
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '#007acc';
        }, 2000);
    }).catch(function(err) {
        console.error('复制失败: ', err);
    });
}
</script>

<!-- 在这里开始编写你的文章内容 -->

<!-- 地址、账号、密码标准展示格式参考：参数保存.md -->

**访问地址：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">访问地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://your-url.com
        <button class="copy-btn" onclick="copyTerminalContent('https://your-url.com', this)">复制</button>
    </div>
</div>

**账号：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">账号</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>your-username
        <button class="copy-btn" onclick="copyTerminalContent('your-username', this)">复制</button>
    </div>
</div>

**密码：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">密码</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>your-password
        <button class="copy-btn" onclick="copyTerminalContent('your-password', this)">复制</button>
    </div>
</div>


<!-- hexo-solitude-tag 插件标签语法参考：

## 提示框标签
{% note info %}
这是信息提示框内容
{% endnote %}

{% note warning %}
这是警告提示框内容
{% endnote %}

{% note success %}
这是成功提示框内容
{% endnote %}

{% note danger %}
这是错误提示框内容
{% endnote %}

## 按钮标签
{% button 'fas fa-home' '按钮文字' 'https://example.com' %}
{% button 'fas fa-download' '下载' '/path/to/file' %}

## 折叠框标签
{% fold 标题, false %}
这里是折叠框的内容
{% endfold %}

## 时间线标签
{% timeline 时间线标题 %}
{% timenode 2024-01-01, 事件标题 %}
事件描述内容
{% endtimenode %}
{% endtimeline %}

## 视频标签
{% youtube 视频ID %}
{% bvideo BV号 %}

## 链接标签
{% link 链接标题 链接描述 https://example.com %}

## 图片标签
{% img /path/to/image.jpg 图片描述 %}
{% inline_img /path/to/image.jpg 图片标题 50px %}

## 复选框和单选框
{% checkbox blue checked 已选中的选项 %}
{% checkbox red unchecked 未选中的选项 %}
{% radio green checked 选中的单选项 %}

## 其他标签
{% span red 红色文字 %}
{% p center 居中段落 %}
{% keyboard Ctrl %} + {% keyboard C %}
{% spoiler 点击显示隐藏内容 %}

-->

<!-- 使用terminal-block格式示例：

**网站地址：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">网站地址</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>https://example.com
        <button class="copy-btn" onclick="copyTerminalContent('https://example.com', this)">复制</button>
    </div>
</div>

**账号：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">账号</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>username
        <button class="copy-btn" onclick="copyTerminalContent('username', this)">复制</button>
    </div>
</div>

**密码：**
<div class="terminal-block">
    <div class="terminal-header">
        <span class="terminal-title">密码</span>
        <div class="terminal-buttons">
            <button class="terminal-btn close"></button>
            <button class="terminal-btn minimize"></button>
            <button class="terminal-btn maximize"></button>
        </div>
    </div>
    <div class="terminal-content">
        <span class="terminal-prompt">$</span>password123
        <button class="copy-btn" onclick="copyTerminalContent('password123', this)">复制</button>
    </div>
</div>

-->

## 免责声明
本文档仅用于学习和教育目的，旨在帮助安全研究人员和开发者了解网络技术。本博文中所包含的信息和工具仅用于合法的安全测试和研究，不得用于任何非法活动。

使用本文所提供的信息进行任何未经授权的行为均为非法行为，违反法律将导致严重的法律后果。读者在使用这些信息时，必须确保拥有合法的授权，并严格遵守所在国家和地区的法律法规。

作者不对任何因使用本文内容而导致的直接或间接损害承担责任。所有风险和责任由用户自行承担。

此外，读者必须在24小时内删除产生的内容，以确保信息不会被滥用。
