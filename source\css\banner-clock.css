/* 横幅时钟组件样式 */
.banner-clock {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 220px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff3838 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  box-sizing: border-box;
  border-radius: 0 15px 15px 0;
  box-shadow: 2px 0 15px rgba(255, 107, 107, 0.3);
  z-index: 10;
  overflow: hidden;
}

.banner-clock::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.banner-clock-content {
  position: relative;
  z-index: 2;
  text-align: center;
  width: 100%;
}

.banner-clock-date {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  opacity: 0.9;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.banner-clock-time {
  font-size: 2.2rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  margin: 0.8rem 0;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  line-height: 1;
}

.banner-clock-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1rem;
  margin-top: 0.5rem;
  opacity: 0.9;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.banner-clock-weather {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.9;
  flex-wrap: wrap;
}

.banner-clock-weather-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.banner-clock-weather i {
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .banner-clock {
    width: 200px;
    padding: 1.2rem;
  }
  
  .banner-clock-time {
    font-size: 2rem;
    letter-spacing: 1.5px;
  }
}

@media (max-width: 768px) {
  .banner-clock {
    position: relative;
    width: 100%;
    border-radius: 12px;
    margin-bottom: 1rem;
    padding: 1.5rem;
  }
  
  .banner-clock-time {
    font-size: 2rem;
    letter-spacing: 1px;
  }
  
  .banner-clock-weather {
    gap: 0.5rem;
  }
}

/* 动画效果 */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.banner-clock {
  animation: fadeInLeft 0.8s ease-out;
}

.banner-clock-time {
  transition: all 0.3s ease;
}

.banner-clock-time:hover {
  transform: scale(1.05);
}

/* 确保横幅容器有相对定位 */
#home_top {
  position: relative;
}

#home_top .container {
  position: relative;
}

#banners {
  position: relative;
}

/* 调整横幅内容的左边距，为时钟腾出空间 */
@media (min-width: 769px) {
  #home_top .banners-title,
  #home_top .tags-group-all,
  #banners .banners-title,
  #banners .tags-group-all {
    margin-left: 240px;
  }
}

/* 深色模式适配 */
[data-theme="dark"] .banner-clock {
  background: linear-gradient(135deg, #c0392b 0%, #8e44ad 50%, #2c3e50 100%);
}

/* 脉动效果 */
@keyframes pulse {
  0%, 100% {
    box-shadow: 2px 0 20px rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 2px 0 30px rgba(255, 107, 107, 0.5);
  }
}

.banner-clock {
  animation: fadeInLeft 0.8s ease-out, pulse 3s ease-in-out infinite;
}