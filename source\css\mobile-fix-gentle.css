/* 温和的移动端修复 - 避免过度使用 !important */
@media (max-width: 768px) {
  /* 基础视口修复 - 只在必要时使用 !important */
  html, body {
    overflow-x: hidden;
    margin: 0;
    padding: 0;
  }
  
  /* 主要容器的温和修复 */
  #article-container {
    max-width: 100%;
    padding: 15px;
    box-sizing: border-box;
  }
  
  /* 文章内容修复 */
  .post-content {
    max-width: 100%;
    overflow-x: hidden;
    word-wrap: break-word;
    box-sizing: border-box;
  }
  
  /* 代码块修复 - 保持原有样式的同时确保不溢出 */
  .terminal-block,
  .highlight,
  pre {
    max-width: 100%;
    overflow-x: auto;
    box-sizing: border-box;
  }
  
  /* 图片修复 */
  .post-content img {
    max-width: 100%;
    height: auto;
  }
  
  /* 表格修复 */
  .post-content table {
    max-width: 100%;
    overflow-x: auto;
    display: block;
  }
  
  /* 横幅时钟移动端优化 */
  #banner-clock {
    width: 160px;
    height: 130px;
    bottom: 15px;
    left: 15px;
    padding: 12px;
  }
  
  /* 位置选择器移动端优化 */
  #location-selector-modal > div {
    width: 95%;
    max-width: 95%;
    margin: 0 auto;
    box-sizing: border-box;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  #article-container {
    padding: 10px;
  }
  
  .post-content {
    font-size: 14px;
    line-height: 1.6;
  }
  
  .post-title {
    font-size: 1.5rem;
  }
  
  #banner-clock {
    width: 140px;
    height: 110px;
    bottom: 10px;
    left: 10px;
    padding: 10px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  #banner-clock {
    width: 140px;
    height: 100px;
    bottom: 10px;
    left: 10px;
  }
}
